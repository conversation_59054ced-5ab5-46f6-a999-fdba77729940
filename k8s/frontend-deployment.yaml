apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: kubernetes-demo
  labels:
    app: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: kubernetes-demo-frontend:latest
        imagePullPolicy: Never  # For local development
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: frontend-config
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
