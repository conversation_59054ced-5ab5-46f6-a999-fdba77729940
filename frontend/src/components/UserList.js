import React from 'react';

const UserList = ({ users, onEdit, onDelete, loading }) => {
  if (loading) {
    return <div className="loading">Loading users...</div>;
  }

  if (!users || users.length === 0) {
    return (
      <div className="users-list">
        <div className="loading">No users found. Add some users to get started!</div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="users-list">
      <h2>Users ({users.length})</h2>
      {users.map(user => (
        <div key={user.id} className="user-item">
          <div className="user-info">
            <h3>{user.name}</h3>
            <p>Email: {user.email}</p>
            <p>Status: {user.is_active ? 'Active' : 'Inactive'}</p>
            <p>Created: {formatDate(user.created_at)}</p>
          </div>
          <div className="user-actions">
            <button 
              className="btn btn-primary"
              onClick={() => onEdit(user)}
            >
              Edit
            </button>
            <button 
              className="btn btn-danger"
              onClick={() => {
                if (window.confirm(`Are you sure you want to delete ${user.name}?`)) {
                  onDelete(user.id);
                }
              }}
            >
              Delete
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default UserList;
