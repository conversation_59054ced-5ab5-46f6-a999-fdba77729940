import React, { useState, useEffect } from 'react';
import { userAPI } from './api';
import UserForm from './components/UserForm';
import UserList from './components/UserList';
import './App.css';

function App() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingUser, setEditingUser] = useState(null);
  const [apiStatus, setApiStatus] = useState('checking');

  // Check API health on component mount
  useEffect(() => {
    checkApiHealth();
  }, []);

  // Load users when component mounts
  useEffect(() => {
    if (apiStatus === 'healthy') {
      loadUsers();
    }
  }, [apiStatus]);

  const checkApiHealth = async () => {
    try {
      await userAPI.healthCheck();
      setApiStatus('healthy');
      setError(null);
    } catch (err) {
      setApiStatus('unhealthy');
      setError('Cannot connect to API. Please make sure the backend server is running.');
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      setLoading(true);
      const userData = await userAPI.getUsers();
      setUsers(userData);
      setError(null);
    } catch (err) {
      setError('Failed to load users: ' + err.message);
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (userData) => {
    try {
      const newUser = await userAPI.createUser(userData);
      setUsers(prev => [...prev, newUser]);
      setError(null);
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message;
      setError('Failed to create user: ' + errorMessage);
      throw err;
    }
  };

  const handleUpdateUser = async (userData) => {
    try {
      const updatedUser = await userAPI.updateUser(editingUser.id, userData);
      setUsers(prev => prev.map(user => 
        user.id === editingUser.id ? updatedUser : user
      ));
      setEditingUser(null);
      setError(null);
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message;
      setError('Failed to update user: ' + errorMessage);
      throw err;
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      await userAPI.deleteUser(userId);
      setUsers(prev => prev.filter(user => user.id !== userId));
      setError(null);
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message;
      setError('Failed to delete user: ' + errorMessage);
    }
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
  };

  const handleCancelEdit = () => {
    setEditingUser(null);
  };

  if (apiStatus === 'checking') {
    return (
      <div className="container">
        <div className="loading">Checking API connection...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <header className="header">
        <h1>🚀 Kubernetes Demo App</h1>
        <p>Simple user management system built with FastAPI and React</p>
        <div className="api-status">
          API Status: <span className={apiStatus === 'healthy' ? 'status-healthy' : 'status-unhealthy'}>
            {apiStatus === 'healthy' ? '✅ Connected' : '❌ Disconnected'}
          </span>
        </div>
      </header>

      {error && (
        <div className="error">
          {error}
          {apiStatus === 'unhealthy' && (
            <button 
              className="btn btn-primary" 
              onClick={checkApiHealth}
              style={{ marginLeft: '1rem' }}
            >
              Retry Connection
            </button>
          )}
        </div>
      )}

      {apiStatus === 'healthy' && (
        <>
          <UserForm 
            onSubmit={editingUser ? handleUpdateUser : handleCreateUser}
            initialData={editingUser}
            onCancel={editingUser ? handleCancelEdit : null}
          />
          
          <UserList 
            users={users}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
            loading={loading}
          />
        </>
      )}
    </div>
  );
}

export default App;
