.App {
  text-align: center;
}

.api-status {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.status-healthy {
  color: #2ecc71;
  font-weight: bold;
}

.status-unhealthy {
  color: #e74c3c;
  font-weight: bold;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .user-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
