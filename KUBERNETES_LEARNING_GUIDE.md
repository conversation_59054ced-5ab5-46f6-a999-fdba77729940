# 📚 Kubernetes Learning Guide

This guide will help you understand Kubernetes concepts using this demo application.

## 🎯 Learning Objectives

By working with this application, you'll learn:

1. **Container Orchestration**: How Kubernetes manages containers
2. **Service Discovery**: How services communicate with each other
3. **Configuration Management**: Using ConfigMaps and Secrets
4. **Storage**: Persistent volumes and claims
5. **Networking**: Services, Ingress, and load balancing
6. **Health Monitoring**: Liveness and readiness probes
7. **Scaling**: Horizontal pod autoscaling

## 🔍 Key Kubernetes Concepts Demonstrated

### 1. Pods
- **What**: Smallest deployable unit in Kubernetes
- **In this app**: Each service (frontend, backend, postgres) runs in pods
- **Explore**: `kubectl get pods -n kubernetes-demo`

### 2. Deployments
- **What**: Manages pod replicas and updates
- **In this app**: Ensures 2 replicas of frontend and backend are always running
- **Explore**: `kubectl get deployments -n kubernetes-demo`

### 3. Services
- **What**: Provides stable network endpoint for pods
- **In this app**: Enables communication between frontend, backend, and database
- **Explore**: `kubectl get services -n kubernetes-demo`

### 4. ConfigMaps
- **What**: Stores configuration data
- **In this app**: Database connection strings and API URLs
- **Explore**: `kubectl get configmaps -n kubernetes-demo -o yaml`

### 5. Secrets
- **What**: Stores sensitive data (passwords, tokens)
- **In this app**: Database credentials
- **Explore**: `kubectl get secrets -n kubernetes-demo`

### 6. Persistent Volumes
- **What**: Provides persistent storage
- **In this app**: PostgreSQL data storage
- **Explore**: `kubectl get pvc -n kubernetes-demo`

### 7. Ingress
- **What**: Manages external access to services
- **In this app**: Routes traffic to frontend and backend
- **Explore**: `kubectl get ingress -n kubernetes-demo`

## 🧪 Hands-on Exercises

### Exercise 1: Explore the Application
```bash
# Deploy the application
./scripts/build-images.sh
./scripts/deploy-k8s.sh

# Check all resources
kubectl get all -n kubernetes-demo

# Access the application
kubectl port-forward -n kubernetes-demo service/frontend-service 3000:80
```

### Exercise 2: Scale the Application
```bash
# Scale backend to 3 replicas
kubectl scale deployment backend --replicas=3 -n kubernetes-demo

# Watch the scaling process
kubectl get pods -n kubernetes-demo -w

# Check the new replica count
kubectl get deployment backend -n kubernetes-demo
```

### Exercise 3: Update Configuration
```bash
# Edit the backend ConfigMap
kubectl edit configmap backend-config -n kubernetes-demo

# Restart backend pods to pick up changes
kubectl rollout restart deployment/backend -n kubernetes-demo

# Watch the rolling update
kubectl rollout status deployment/backend -n kubernetes-demo
```

### Exercise 4: Simulate Pod Failure
```bash
# Delete a backend pod
kubectl delete pod -l app=backend -n kubernetes-demo --force

# Watch Kubernetes recreate it
kubectl get pods -n kubernetes-demo -w
```

### Exercise 5: Check Application Health
```bash
# View pod health status
kubectl describe pod -l app=backend -n kubernetes-demo

# Check logs
kubectl logs -l app=backend -n kubernetes-demo

# Test health endpoints
kubectl port-forward -n kubernetes-demo service/backend-service 8000:8000
curl http://localhost:8000/health
```

## 🔧 Troubleshooting Exercises

### Debug a Failing Pod
```bash
# If a pod is not starting
kubectl describe pod <pod-name> -n kubernetes-demo
kubectl logs <pod-name> -n kubernetes-demo

# Check events
kubectl get events -n kubernetes-demo --sort-by='.lastTimestamp'
```

### Network Connectivity Issues
```bash
# Test service connectivity from within cluster
kubectl run debug --image=busybox -it --rm --restart=Never -n kubernetes-demo -- sh

# Inside the debug pod:
nslookup backend-service
wget -qO- http://backend-service:8000/health
```

## 📊 Monitoring and Observability

### View Resource Usage
```bash
# Pod resource usage
kubectl top pods -n kubernetes-demo

# Node resource usage
kubectl top nodes
```

### Application Metrics
```bash
# Check deployment status
kubectl get deployments -n kubernetes-demo

# View replica sets
kubectl get rs -n kubernetes-demo

# Check service endpoints
kubectl get endpoints -n kubernetes-demo
```

## 🚀 Advanced Exercises

### 1. Implement Horizontal Pod Autoscaler
```bash
# Create HPA for backend
kubectl autoscale deployment backend --cpu-percent=50 --min=2 --max=10 -n kubernetes-demo

# Generate load and watch scaling
kubectl get hpa -n kubernetes-demo -w
```

### 2. Add Resource Limits and Requests
Edit the deployment files to add:
```yaml
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

### 3. Implement Network Policies
Create network policies to restrict traffic between services.

### 4. Add Persistent Volume for Logs
Create a shared volume for application logs.

## 🎓 Next Steps

1. **Learn about Helm**: Package manager for Kubernetes
2. **Explore Operators**: Automate complex applications
3. **Study GitOps**: Continuous deployment with ArgoCD or Flux
4. **Security**: RBAC, Pod Security Standards, Network Policies
5. **Monitoring**: Prometheus, Grafana, Jaeger
6. **Service Mesh**: Istio or Linkerd

## 📖 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Kubernetes Tutorials](https://kubernetes.io/docs/tutorials/)
- [kubectl Cheat Sheet](https://kubernetes.io/docs/reference/kubectl/cheatsheet/)
- [Kubernetes Best Practices](https://kubernetes.io/docs/concepts/configuration/overview/)

Happy learning! 🎉
