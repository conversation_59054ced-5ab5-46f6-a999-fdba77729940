# 🚀 Kubernetes Demo Application

A simple full-stack application built with **FastAPI**, **React**, and **PostgreSQL** designed to help you learn Kubernetes fundamentals.

## 📋 What You'll Learn

- Container orchestration with Kubernetes
- Deploying multi-tier applications
- Service discovery and networking
- ConfigMaps and Secrets management
- Persistent storage with PVCs
- Health checks and probes
- Ingress controllers and load balancing

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  FastAPI Backend │    │   PostgreSQL    │
│     (Port 80)    │◄──►│    (Port 8000)   │◄──►│   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Prerequisites

- **Docker** and **Docker Compose**
- **Kubernetes cluster** (minikube, kind, or cloud provider)
- **kubectl** CLI tool
- **Node.js** 18+ (for local development)
- **Python** 3.11+ (for local development)

## 🚀 Quick Start

### Option 1: Docker Compose (Recommended for beginners)

```bash
# Navigate to the project directory
cd kubernetes-demo

# Start all services
docker-compose up --build

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Kubernetes Deployment

#### Step 1: Setup Kubernetes (First Time Only)

If you don't have Kubernetes running yet:
```bash
# Check if Kubernetes is ready
./scripts/check-k8s.sh

# If not ready, follow the setup guide
cat SETUP_KUBERNETES.md
```

#### Step 2: Build and Deploy

```bash
# Build Docker images
./scripts/build-images.sh

# Deploy to Kubernetes
./scripts/deploy-k8s.sh
```

#### Step 3: Access the Application

```bash
# Port forward to access services
kubectl port-forward -n kubernetes-demo service/frontend-service 3000:80 &
kubectl port-forward -n kubernetes-demo service/backend-service 8000:8000 &

# Open in browser
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000/docs
```

## 📁 Project Structure

```
kubernetes-demo/
├── backend/                 # FastAPI application
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI app and routes
│   │   ├── database.py     # Database connection
│   │   ├── models.py       # SQLAlchemy models
│   │   ├── schemas.py      # Pydantic schemas
│   │   └── crud.py         # Database operations
│   ├── Dockerfile
│   ├── requirements.txt
│   └── .env.example
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── App.js         # Main App component
│   │   ├── api.js         # API client
│   │   └── index.js       # Entry point
│   ├── Dockerfile
│   ├── nginx.conf
│   └── package.json
├── k8s/                   # Kubernetes manifests
│   ├── namespace.yaml
│   ├── postgres-*.yaml    # PostgreSQL resources
│   ├── backend-*.yaml     # Backend resources
│   ├── frontend-*.yaml    # Frontend resources
│   └── ingress.yaml
├── docker-compose.yml     # Local development
└── README.md
```

## 🔧 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Welcome message |
| GET | `/health` | Health check |
| GET | `/users/` | List all users |
| POST | `/users/` | Create new user |
| GET | `/users/{id}` | Get user by ID |
| PUT | `/users/{id}` | Update user |
| DELETE | `/users/{id}` | Delete user |
| GET | `/docs` | Interactive API documentation |

## 🐛 Troubleshooting

### Common Issues

1. **Pods not starting**: Check logs with `kubectl logs <pod-name> -n kubernetes-demo`
2. **Database connection issues**: Ensure PostgreSQL pod is running and healthy
3. **Image pull errors**: Make sure images are built locally or available in registry

### Useful Commands

```bash
# Check all resources
kubectl get all -n kubernetes-demo

# View pod logs
kubectl logs -f deployment/backend -n kubernetes-demo

# Describe a resource
kubectl describe pod <pod-name> -n kubernetes-demo

# Delete all resources
kubectl delete namespace kubernetes-demo
```

## 📚 Learning Resources

- [Kubernetes Official Documentation](https://kubernetes.io/docs/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/)
- [Docker Documentation](https://docs.docker.com/)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
