# 🚀 Quick Start Guide

Get up and running with the Kubernetes Demo in minutes!

## 🎯 Choose Your Path

### Path 1: Docker Compose (Easiest - Recommended for beginners)
Perfect for understanding the application before moving to Kubernetes.

```bash
# Start all services
docker-compose up --build

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000/docs
```

### Path 2: Kubernetes (Learning Kubernetes)
Deploy the full application on Kubernetes.

```bash
# Step 1: Build Docker images
./scripts/build-images.sh

# Step 2: Deploy to Kubernetes
./scripts/deploy-k8s.sh

# Step 3: Access the application
kubectl port-forward -n kubernetes-demo service/frontend-service 3000:80
kubectl port-forward -n kubernetes-demo service/backend-service 8000:8000
```

## 🧪 Test the Application

1. **Open Frontend**: http://localhost:3000
2. **Add a User**: Fill in name and email, click "Add User"
3. **View Users**: See your user in the list below
4. **Edit/Delete**: Use the buttons to modify users
5. **API Docs**: Visit http://localhost:8000/docs for interactive API documentation

## 🔧 Troubleshooting

### Docker Compose Issues
```bash
# View logs
docker-compose logs

# Restart services
docker-compose restart

# Clean up
docker-compose down -v
```

### Kubernetes Issues
```bash
# Check pod status
kubectl get pods -n kubernetes-demo

# View logs
kubectl logs -l app=backend -n kubernetes-demo

# Clean up
./scripts/cleanup.sh
```

## 📚 What's Next?

1. **Explore the code**: Check out the FastAPI backend and React frontend
2. **Learn Kubernetes**: Follow the [Kubernetes Learning Guide](KUBERNETES_LEARNING_GUIDE.md)
3. **Experiment**: Try scaling, updating, and monitoring the application

## 🆘 Need Help?

- Check the main [README.md](README.md) for detailed documentation
- Review the [Kubernetes Learning Guide](KUBERNETES_LEARNING_GUIDE.md) for hands-on exercises
- Look at the application logs for error messages

Happy learning! 🎉
