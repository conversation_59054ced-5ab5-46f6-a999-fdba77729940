# 🚀 Kubernetes Setup Guide

You need a Kubernetes cluster to deploy the application. Here are your options:

## Option 1: Miniku<PERSON> (Recommended for Learning)

### Install Minikube (if not already installed)
```bash
# For Linux
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube
```

### Start Minikube
```bash
# Start minikube (this may take a few minutes on first run)
minikube start

# Verify it's running
minikube status
kubectl get nodes
```

## Option 2: Kind (Kubernetes in Docker)

### Install Kind
```bash
# For Linux
curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
chmod +x ./kind
sudo mv ./kind /usr/local/bin/kind
```

### Create a Kind Cluster
```bash
# Create cluster
kind create cluster --name kubernetes-demo

# Verify it's running
kubectl cluster-info --context kind-kubernetes-demo
```

## Option 3: Cloud Provider (Advanced)
- **Google GKE**: `gcloud container clusters create kubernetes-demo`
- **AWS EKS**: Use `eksctl create cluster`
- **Azure AKS**: Use `az aks create`

## Once Kubernetes is Running

### 1. Build and Deploy the Application
```bash
# Build Docker images
./scripts/build-images.sh

# Deploy to Kubernetes
./scripts/deploy-k8s.sh
```

### 2. Access the Application
```bash
# Port forward to access services
kubectl port-forward -n kubernetes-demo service/frontend-service 3000:80 &
kubectl port-forward -n kubernetes-demo service/backend-service 8000:8000 &

# Open in browser
echo "Frontend: http://localhost:3000"
echo "Backend API: http://localhost:8000/docs"
```

### 3. Try Scaling (Your Original Command!)
```bash
# Scale backend to 3 replicas
kubectl scale deployment backend --replicas=3 -n kubernetes-demo

# Watch the scaling happen
kubectl get pods -n kubernetes-demo -w
```

## Troubleshooting

### If kubectl is not found
```bash
# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/
```

### If images can't be pulled
```bash
# For minikube, load images directly
minikube image load kubernetes-demo-backend:latest
minikube image load kubernetes-demo-frontend:latest

# For kind, load images directly
kind load docker-image kubernetes-demo-backend:latest --name kubernetes-demo
kind load docker-image kubernetes-demo-frontend:latest --name kubernetes-demo
```

### Check cluster status
```bash
kubectl get nodes
kubectl get pods --all-namespaces
kubectl cluster-info
```

## Next Steps

Once you have Kubernetes running:

1. **Deploy the app**: `./scripts/deploy-k8s.sh`
2. **Scale services**: `kubectl scale deployment backend --replicas=3 -n kubernetes-demo`
3. **Monitor**: `kubectl get pods -n kubernetes-demo -w`
4. **Learn**: Follow the [Kubernetes Learning Guide](KUBERNETES_LEARNING_GUIDE.md)

The application is ready to go - you just need to get Kubernetes running first! 🎉
