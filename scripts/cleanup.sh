#!/bin/bash

# Cleanup Kubernetes resources
echo "🧹 Cleaning up Kubernetes Demo resources..."

# Delete the namespace (this will delete all resources in it)
kubectl delete namespace kubernetes-demo

if [ $? -eq 0 ]; then
    echo "✅ Namespace and all resources deleted successfully"
else
    echo "❌ Failed to delete resources"
    exit 1
fi

echo ""
echo "🎉 Cleanup completed!"
echo ""
echo "Optional: Remove Docker images"
echo "docker rmi kubernetes-demo-backend:latest"
echo "docker rmi kubernetes-demo-frontend:latest"
