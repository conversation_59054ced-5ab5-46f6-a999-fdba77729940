#!/bin/bash

# Deploy application to Kubernetes
echo "🚀 Deploying Kubernetes Demo to cluster..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Apply all Kubernetes manifests
echo "Applying Kubernetes manifests..."
kubectl apply -f k8s/

if [ $? -eq 0 ]; then
    echo "✅ Manifests applied successfully"
else
    echo "❌ Failed to apply manifests"
    exit 1
fi

echo ""
echo "⏳ Waiting for deployments to be ready..."

# Wait for deployments to be ready
kubectl wait --for=condition=available --timeout=300s deployment/postgres -n kubernetes-demo
kubectl wait --for=condition=available --timeout=300s deployment/backend -n kubernetes-demo
kubectl wait --for=condition=available --timeout=300s deployment/frontend -n kubernetes-demo

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📊 Current status:"
kubectl get pods -n kubernetes-demo
echo ""
kubectl get services -n kubernetes-demo
echo ""
echo "🌐 Access the application:"
echo "Frontend: kubectl port-forward -n kubernetes-demo service/frontend-service 3000:80"
echo "Backend: kubectl port-forward -n kubernetes-demo service/backend-service 8000:8000"
echo "API Docs: http://localhost:8000/docs"
