#!/bin/bash

# Check if Kubernetes is ready for deployment
echo "🔍 Checking Kubernetes setup..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    echo "📖 Please follow the setup guide: SETUP_KUBERNETES.md"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster"
    echo "💡 Try one of these:"
    echo "   - minikube start"
    echo "   - kind create cluster --name kubernetes-demo"
    echo "📖 See SETUP_KUBERNETES.md for detailed instructions"
    exit 1
fi

echo "✅ kubectl is available"
echo "✅ Kubernetes cluster is accessible"

# Show cluster info
echo ""
echo "📊 Cluster Information:"
kubectl cluster-info
echo ""

# Show nodes
echo "🖥️  Cluster Nodes:"
kubectl get nodes
echo ""

# Check if Docker images exist
echo "🐳 Checking Docker images..."
if docker image inspect kubernetes-demo-backend:latest &> /dev/null; then
    echo "✅ Backend image exists"
else
    echo "❌ Backend image not found - run: ./scripts/build-images.sh"
fi

if docker image inspect kubernetes-demo-frontend:latest &> /dev/null; then
    echo "✅ Frontend image exists"
else
    echo "❌ Frontend image not found - run: ./scripts/build-images.sh"
fi

echo ""
echo "🎉 Ready to deploy! Run: ./scripts/deploy-k8s.sh"
