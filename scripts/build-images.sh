#!/bin/bash

# Build Docker images for Kubernetes deployment
echo "🐳 Building Docker images for Kubernetes Demo..."

# Build backend image
echo "Building backend image..."
docker build -t kubernetes-demo-backend:latest ./backend

if [ $? -eq 0 ]; then
    echo "✅ Backend image built successfully"
else
    echo "❌ Failed to build backend image"
    exit 1
fi

# Build frontend image
echo "Building frontend image..."
docker build -t kubernetes-demo-frontend:latest ./frontend

if [ $? -eq 0 ]; then
    echo "✅ Frontend image built successfully"
else
    echo "❌ Failed to build frontend image"
    exit 1
fi

echo "🎉 All images built successfully!"
echo ""
echo "Next steps:"
echo "1. Deploy to Kubernetes: kubectl apply -f k8s/"
echo "2. Check status: kubectl get pods -n kubernetes-demo"
